package com.example.pure.demo;

import com.example.pure.util.SnowflakeIdGenerator;
import com.example.pure.util.SpringEncryptionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AES-GCM算法演示类
 * 展示新的兼容密钥生成和解析功能
 */
@Slf4j
public class AESGCMDemo {

    public static void main(String[] args) {
        try {
            // 初始化组件
            SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(1, 1);
            SpringEncryptionUtil encryptionUtil = new SpringEncryptionUtil(snowflakeIdGenerator);
            
            // 设置加密参数（实际使用中应从配置文件读取）
            java.lang.reflect.Field passwordField = SpringEncryptionUtil.class.getDeclaredField("encryptionPassword");
            passwordField.setAccessible(true);
            passwordField.set(encryptionUtil, "demoPassword123");

            java.lang.reflect.Field saltField = SpringEncryptionUtil.class.getDeclaredField("encryptionSalt");
            saltField.setAccessible(true);
            saltField.set(encryptionUtil, "64656d6f53616c743435363738393031323334353637383930313233343536373839"); // hex编码的盐值
            
            // 初始化加密工具
            encryptionUtil.init();
            
            log.info("=== AES-GCM兼容密钥算法演示 ===");
            
            // 演示1：基本的AES-GCM加密解密
            demonstrateBasicEncryption(encryptionUtil);
            
            // 演示2：兼容密钥生成和解析
            demonstrateCompatibleKeyGeneration(encryptionUtil);
            
            // 演示3：性能测试
            demonstratePerformance(encryptionUtil);
            
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        }
    }

    /**
     * 演示基本的AES-GCM加密解密
     */
    private static void demonstrateBasicEncryption(SpringEncryptionUtil encryptionUtil) {
        log.info("\n--- 演示1：基本AES-GCM加密解密 ---");
        
        String plaintext = "用户ID:12345|时间戳:1699123456789|密钥名称:demo-key|随机数:987654321";
        log.info("原始数据: {}", plaintext);
        
        String encrypted = encryptionUtil.encryptWithGCM(plaintext);
        log.info("加密结果: {}", encrypted);
        log.info("加密长度: {} 字符", encrypted.length());
        
        String decrypted = encryptionUtil.decryptWithGCM(encrypted);
        log.info("解密结果: {}", decrypted);
        log.info("解密成功: {}", plaintext.equals(decrypted));
    }

    /**
     * 演示兼容密钥生成和解析
     */
    private static void demonstrateCompatibleKeyGeneration(SpringEncryptionUtil encryptionUtil) {
        log.info("\n--- 演示2：兼容密钥生成和解析 ---");
        
        Long userId = 12345L;
        String keyName = "demo-api-key";
        
        // 生成兼容密钥
        SpringEncryptionUtil.CompatibleKeyResult result =
            encryptionUtil.generateCompatibleApiKey(userId, keyName);

        log.info("生成的兼容密钥: {}", result.getCompatibleKey());
        log.info("密钥长度: {} 字符", result.getCompatibleKey().length());
        log.info("安全哈希: {}", result.getSecurityHash());
        log.info("Snowflake ID: {}", result.getSnowflakeId());

        // 解析兼容密钥
        SpringEncryptionUtil.ParseResultV2 parseResult =
            encryptionUtil.parseCompatibleApiKey(result.getCompatibleKey());
        
        log.info("解析结果:");
        log.info("  - 有效性: {}", parseResult.isValid());
        log.info("  - 用户ID: {}", parseResult.getUserId());
        log.info("  - 密钥名称: {}", parseResult.getKeyName());
        log.info("  - 时间戳: {}", parseResult.getTimestamp());
        log.info("  - 消息: {}", parseResult.getMessage());
        
        // 验证密钥
        boolean isValid = encryptionUtil.validateCompatibleKey(result.getCompatibleKey());
        log.info("密钥验证结果: {}", isValid);
    }

    /**
     * 演示性能测试
     */
    private static void demonstratePerformance(SpringEncryptionUtil encryptionUtil) {
        log.info("\n--- 演示3：性能测试 ---");
        
        int testCount = 1000;
        Long userId = 99999L;
        String keyName = "performance-test";
        
        // 测试密钥生成性能
        long startTime = System.nanoTime();
        for (int i = 0; i < testCount; i++) {
            encryptionUtil.generateCompatibleApiKey(userId, keyName + i);
        }
        long endTime = System.nanoTime();
        double avgGenerationTime = (endTime - startTime) / 1_000_000.0 / testCount;
        log.info("密钥生成性能: 平均 {:.3f} 毫秒/次 ({} 次测试)", avgGenerationTime, testCount);

        // 测试密钥解析性能
        SpringEncryptionUtil.CompatibleKeyResult testKey =
            encryptionUtil.generateCompatibleApiKey(userId, keyName);

        startTime = System.nanoTime();
        for (int i = 0; i < testCount; i++) {
            encryptionUtil.parseCompatibleApiKey(testKey.getCompatibleKey());
        }
        endTime = System.nanoTime();
        double avgParsingTime = (endTime - startTime) / 1_000_000.0 / testCount;
        log.info("密钥解析性能: 平均 {:.3f} 毫秒/次 ({} 次测试)", avgParsingTime, testCount);
        
        log.info("\n安全特性:");
        log.info("  ✅ AES-256-GCM加密 - 提供机密性和完整性保护");
        log.info("  ✅ 随机IV - 每次加密使用不同的初始化向量");
        log.info("  ✅ 认证标签 - 防止数据篡改");
        log.info("  ✅ 用户信息加密 - 无法从密钥中直接获取用户信息");
        log.info("  ✅ 时间戳保护 - 支持密钥过期验证");
        log.info("  ✅ OpenAI兼容格式 - 以sk-v2-开头，保持兼容性");
    }
}
