package com.example.pure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.security.crypto.keygen.KeyGenerators;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.security.spec.KeySpec;
import java.util.Base64;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Spring加密工具类
 * <p>
 * 使用Spring Security的加密器进行API密钥加密和解密
 * </p>
 */
@Slf4j
@Component
public class SpringEncryptionUtil {

    @Value("${app.encryption.password:defaultPassword123}")
    private String encryptionPassword;

    @Value("${app.encryption.salt:defaultSalt456}")
    private String encryptionSalt;

    private TextEncryptor textEncryptor;
    private SecretKey aesGcmKey;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    public SpringEncryptionUtil(SnowflakeIdGenerator snowflakeIdGenerator) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
    }

    @PostConstruct
    public void init() {
        try {
            // 现有的CBC模式加密器（向后兼容）
            this.textEncryptor = Encryptors.text(encryptionPassword, encryptionSalt);

            // 新增AES-GCM密钥
            this.aesGcmKey = deriveAESGCMKey(encryptionPassword, encryptionSalt);

            log.info("Spring加密工具初始化成功（支持CBC和GCM模式）");
        } catch (Exception e) {
            log.error("Spring加密工具初始化失败", e);
            throw new RuntimeException("加密工具初始化失败", e);
        }
    }

    /**
     * 生成AES-GCM密钥
     * 使用PBKDF2从密码和盐值派生256位密钥
     */
    private SecretKey deriveAESGCMKey(String password, String salt) throws Exception {
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        KeySpec spec = new PBEKeySpec(password.toCharArray(), salt.getBytes(StandardCharsets.UTF_8), 100000, 256);
        SecretKey tmp = factory.generateSecret(spec);
        return new SecretKeySpec(tmp.getEncoded(), "AES");
    }

    /**
     * 加密API密钥
     *
     * @param apiKey 原始API密钥
     * @return 加密后的API密钥
     */
    public String encrypt(String apiKey) {
        try {
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("API密钥不能为空");
            }
            return textEncryptor.encrypt(apiKey);
        } catch (Exception e) {
            log.error("加密API密钥失败", e);
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 解密API密钥
     *
     * @param encryptedApiKey 加密后的API密钥
     * @return 原始API密钥
     */
    public String decrypt(String encryptedApiKey) {
        try {
            if (encryptedApiKey == null || encryptedApiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("加密的API密钥不能为空");
            }
            return textEncryptor.decrypt(encryptedApiKey);
        } catch (Exception e) {
            log.error("解密API密钥失败", e);
            throw new RuntimeException("解密失败", e);
        }
    }

    /**
     * AES-GCM加密（用于兼容密钥生成）
     * 提供认证加密，同时保证机密性和完整性
     *
     * @param plaintext 明文数据
     * @return Base64编码的加密数据（包含IV）
     */
    public String encryptWithGCM(String plaintext) {
        try {
            if (plaintext == null || plaintext.trim().isEmpty()) {
                throw new IllegalArgumentException("明文数据不能为空");
            }

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");

            // 生成随机IV（12字节，GCM推荐长度）
            byte[] iv = new byte[12];
            SecureRandom.getInstanceStrong().nextBytes(iv);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv); // 128位认证标签

            cipher.init(Cipher.ENCRYPT_MODE, aesGcmKey, gcmSpec);
            byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // 组合IV和密文：[IV(12字节)][密文+认证标签]
            byte[] result = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, result, 0, iv.length);
            System.arraycopy(encrypted, 0, result, iv.length, encrypted.length);

            return Base64.getUrlEncoder().withoutPadding().encodeToString(result);
        } catch (Exception e) {
            log.error("AES-GCM加密失败", e);
            throw new RuntimeException("AES-GCM加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * AES-GCM解密（用于兼容密钥解析）
     * 验证完整性并解密数据
     *
     * @param encryptedData Base64编码的加密数据
     * @return 解密后的明文
     */
    public String decryptWithGCM(String encryptedData) {
        try {
            if (encryptedData == null || encryptedData.trim().isEmpty()) {
                throw new IllegalArgumentException("加密数据不能为空");
            }

            byte[] data = Base64.getUrlDecoder().decode(encryptedData);

            if (data.length < 12) {
                throw new IllegalArgumentException("加密数据格式无效：长度不足");
            }

            // 分离IV和密文
            byte[] iv = new byte[12];
            byte[] encrypted = new byte[data.length - 12];
            System.arraycopy(data, 0, iv, 0, 12);
            System.arraycopy(data, 12, encrypted, 0, encrypted.length);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.DECRYPT_MODE, aesGcmKey, gcmSpec);

            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES-GCM解密失败: {}", encryptedData, e);
            throw new RuntimeException("AES-GCM解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成OpenAI格式的兼容API密钥
     * 格式：sk-{encodedUserId}_{timestamp}_{hash}
     *
     * @param userId 用户ID
     * @param keyName 密钥名称（用于生成哈希）
     * @return 兼容格式的API密钥
     */
    public CompatibleKeyResult generateCompatibleApiKey(Long userId, String keyName) {
        try {
            // 编码用户ID（简单可逆编码）
            String encodedUserId = encodeUserId(userId);

            // 生成时间戳
            long timestamp = System.currentTimeMillis();

            // 生成安全哈希
            String data = userId + "_" + keyName + "_" + timestamp;
            String securityHash = generateHash(data);
            String shortHash = securityHash.substring(0, 8); // 取前8位

            // 组装OpenAI格式的兼容密钥
            String compatibleKey = String.format("sk-%s_%d_%s", encodedUserId, timestamp, shortHash);

            // 使用雪花ID作为内部标识
            long snowflakeId = snowflakeIdGenerator.nextId();

            return new CompatibleKeyResult(snowflakeId, compatibleKey, securityHash);
        } catch (Exception e) {
            log.error("生成兼容API密钥失败", e);
            throw new RuntimeException("生成兼容API密钥失败", e);
        }
    }

    /**
     * 编码用户ID（可逆的简单编码）
     * 使用简单的数学变换 + Base36编码
     */
    private String encodeUserId(Long userId) {
        try {
            // 简单的数学变换（可逆）
            long transformed = (userId * 7919 + 12345) ^ 0xABCDEF; // 使用质数和异或

            // 转换为Base36字符串（包含数字和字母）
            String encoded = Long.toString(Math.abs(transformed), 36).toUpperCase();

            // 确保长度一致，不足补0
            return String.format("%12s", encoded).replace(' ', '0');
        } catch (Exception e) {
            log.error("编码用户ID失败", e);
            throw new RuntimeException("编码用户ID失败", e);
        }
    }

    /**
     * 解码用户ID（从编码字符串恢复原始用户ID）
     */
    public Long decodeUserId(String encodedUserId) {
        try {
            // 从Base36字符串转换回数字
            long transformed = Long.parseLong(encodedUserId.trim(), 36);

            // 反向数学变换
            long userId = ((transformed ^ 0xABCDEF) - 12345) / 7919;

            return userId;
        } catch (Exception e) {
            log.error("解码用户ID失败: {}", encodedUserId, e);
            throw new RuntimeException("解码用户ID失败", e);
        }
    }

    /**
     * 解析OpenAI格式的兼容API密钥
     * 从格式 sk-{encodedUserId}_{timestamp}_{hash} 中提取信息
     *
     * @param compatibleKey 兼容格式的API密钥
     * @return 解析结果
     */
    public ParseResult parseCompatibleApiKey(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return new ParseResult(false, null, null, null);
            }

            String[] parts = compatibleKey.substring(3).split("_"); // 去掉 "sk-"
            if (parts.length != 3) {
                return new ParseResult(false, null, null, null);
            }

            String encodedUserId = parts[0];
            Long timestamp = Long.parseLong(parts[1]);
            String hash = parts[2];

            // 解码用户ID
            Long userId = decodeUserId(encodedUserId);

            return new ParseResult(true, userId, timestamp, hash);
        } catch (Exception e) {
            log.warn("解析兼容API密钥失败: {}", compatibleKey, e);
            return new ParseResult(false, null, null, null);
        }
    }

    /**
     * 验证兼容API密钥的哈希值
     *
     * @param compatibleKey 兼容格式的API密钥
     * @param expectedHash 期望的完整哈希值
     * @return 是否有效
     */
    public boolean validateCompatibleApiKey(String compatibleKey, String expectedHash) {
        ParseResult result = parseCompatibleApiKey(compatibleKey);
        if (!result.isValid()) {
            return false;
        }

        // 比较哈希值的前6位
        return expectedHash != null && expectedHash.startsWith(result.getHash());
    }

    /**
     * 升级版兼容密钥生成（使用AES-GCM）
     * 格式：sk-v2-{base64(aes_gcm_encrypted_data)}
     *
     * @param userId 用户ID
     * @param keyName 密钥名称
     * @return 兼容密钥结果
     */
    public CompatibleKeyResult generateCompatibleApiKeyV2(Long userId, String keyName) {
        try {
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID不能为空或小于等于0");
            }
            if (keyName == null || keyName.trim().isEmpty()) {
                keyName = "default";
            }

            // 构造明文数据：userId|timestamp|keyName|random
            String plaintext = String.format("%d|%d|%s|%d",
                userId,
                System.currentTimeMillis(),
                keyName.trim(),
                ThreadLocalRandom.current().nextLong()
            );

            // 使用AES-GCM加密
            String encrypted = encryptWithGCM(plaintext);
            String compatibleKey = "sk-v2-" + encrypted;

            // 生成安全哈希用于数据库存储验证
            String securityHash = generateHash(plaintext);
            Long snowflakeId = snowflakeIdGenerator.nextId();

            log.debug("生成兼容API密钥V2成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return new CompatibleKeyResult(snowflakeId, compatibleKey, securityHash);
        } catch (Exception e) {
            log.error("生成兼容API密钥V2失败 - 用户ID: {}, 密钥名称: {}", userId, keyName, e);
            throw new RuntimeException("生成兼容API密钥V2失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析兼容密钥V2（使用AES-GCM）
     * 从密钥中提取用户信息，用于负载均衡
     *
     * @param compatibleKey 兼容密钥
     * @return 解析结果
     */
    public ParseResultV2 parseCompatibleApiKeyV2(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-v2-")) {
                return new ParseResultV2(false, null, null, null, "密钥格式无效");
            }

            String encrypted = compatibleKey.substring(6); // 去掉"sk-v2-"
            String plaintext = decryptWithGCM(encrypted);
            String[] parts = plaintext.split("\\|");

            if (parts.length != 4) {
                return new ParseResultV2(false, null, null, null, "密钥数据格式无效");
            }

            Long userId = Long.parseLong(parts[0]);
            Long timestamp = Long.parseLong(parts[1]);
            String keyName = parts[2];

            // 验证时间戳（密钥有效期：1年）
            long currentTime = System.currentTimeMillis();
            if (currentTime - timestamp > 365L * 24 * 60 * 60 * 1000) {
                return new ParseResultV2(false, userId, timestamp, keyName, "密钥已过期");
            }

            log.debug("解析兼容API密钥V2成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return new ParseResultV2(true, userId, timestamp, keyName, "解析成功");
        } catch (Exception e) {
            log.warn("解析兼容API密钥V2失败: {}", compatibleKey, e);
            return new ParseResultV2(false, null, null, null, "解析失败: " + e.getMessage());
        }
    }

    /**
     * 验证兼容密钥（支持新旧格式）
     */
    public boolean validateCompatibleKey(String compatibleKey) {
        try {
            if (compatibleKey == null) {
                return false;
            }

            if (compatibleKey.startsWith("sk-v2-")) {
                // 新格式：使用AES-GCM解析
                ParseResultV2 result = parseCompatibleApiKeyV2(compatibleKey);
                return result.isValid();
            } else if (compatibleKey.startsWith("sk-")) {
                // 旧格式：使用现有算法验证
                return validateCompatibleKeyV1(compatibleKey);
            } else {
                return false;
            }
        } catch (Exception e) {
            log.warn("验证兼容密钥失败: {}", compatibleKey, e);
            return false;
        }
    }

    /**
     * 验证兼容密钥V1（原有算法）
     */
    private boolean validateCompatibleKeyV1(String compatibleKey) {
        try {
            // 解析密钥格式：sk-{encodedUserId}_{timestamp}_{hash}
            String keyContent = compatibleKey.substring(3); // 去掉 "sk-"
            String[] parts = keyContent.split("_");

            if (parts.length != 3) {
                return false;
            }

            // 验证用户ID编码
            String encodedUserId = parts[0];
            Long userId = decodeUserId(encodedUserId);
            if (userId == null || userId <= 0) {
                return false;
            }

            // 验证时间戳
            long timestamp = Long.parseLong(parts[1]);
            long currentTime = System.currentTimeMillis();
            // 密钥有效期：1年
            if (currentTime - timestamp > 365L * 24 * 60 * 60 * 1000) {
                return false;
            }

            // 验证哈希
            String providedHash = parts[2];
            String data = userId + "_default_" + timestamp; // 使用默认keyName
            String expectedHash = generateHash(data);
            String expectedShortHash = expectedHash.substring(0, 8);

            return expectedShortHash.equals(providedHash);
        } catch (Exception e) {
            log.warn("验证兼容密钥V1失败: {}", compatibleKey, e);
            return false;
        }
    }

    /**
     * 生成哈希值
     */
    private String generateHash(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes).replaceAll("[^a-zA-Z0-9]", "");
    }

    /**
     * 脱敏显示API密钥
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        
        if (apiKey.startsWith("sk-")) {
            // OpenAI格式密钥脱敏
            return apiKey.substring(0, 7) + "****" + apiKey.substring(apiKey.length() - 4);
        } else {
            // 其他格式密钥脱敏
            return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
        }
    }

    /**
     * 兼容密钥生成结果
     */
    public static class CompatibleKeyResult {
        private final Long snowflakeId;
        private final String compatibleKey;
        private final String securityHash;

        public CompatibleKeyResult(Long snowflakeId, String compatibleKey, String securityHash) {
            this.snowflakeId = snowflakeId;
            this.compatibleKey = compatibleKey;
            this.securityHash = securityHash;
        }

        public Long getSnowflakeId() { return snowflakeId; }
        public String getCompatibleKey() { return compatibleKey; }
        public String getSecurityHash() { return securityHash; }
    }

    /**
     * 解析结果
     */
    public static class ParseResult {
        private final boolean valid;
        private final Long userId;
        private final Long timestamp;
        private final String hash;

        public ParseResult(boolean valid, Long userId, Long timestamp, String hash) {
            this.valid = valid;
            this.userId = userId;
            this.timestamp = timestamp;
            this.hash = hash;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public Long getTimestamp() { return timestamp; }
        public String getHash() { return hash; }
    }

    /**
     * 解析结果V2（支持密钥名称和错误信息）
     */
    public static class ParseResultV2 {
        private final boolean valid;
        private final Long userId;
        private final Long timestamp;
        private final String keyName;
        private final String message;

        public ParseResultV2(boolean valid, Long userId, Long timestamp, String keyName, String message) {
            this.valid = valid;
            this.userId = userId;
            this.timestamp = timestamp;
            this.keyName = keyName;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public Long getTimestamp() { return timestamp; }
        public String getKeyName() { return keyName; }
        public String getMessage() { return message; }
    }
}
