package com.example.pure.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * SpringEncryptionUtil 测试类
 * 测试AES-GCM算法的加密解密功能
 */
@ExtendWith(MockitoExtension.class)
class SpringEncryptionUtilTest {

    @Mock
    private SnowflakeIdGenerator snowflakeIdGenerator;

    private SpringEncryptionUtil springEncryptionUtil;

    @BeforeEach
    void setUp() {
        springEncryptionUtil = new SpringEncryptionUtil(snowflakeIdGenerator);
        
        // 设置测试用的加密参数
        ReflectionTestUtils.setField(springEncryptionUtil, "encryptionPassword", "testPassword123");
        ReflectionTestUtils.setField(springEncryptionUtil, "encryptionSalt", "testSalt456");
        
        // 模拟Snowflake ID生成
        when(snowflakeIdGenerator.nextId()).thenReturn(123456789L);
        
        // 初始化加密工具
        springEncryptionUtil.init();
    }

    @Test
    void testAESGCMEncryptDecrypt() {
        // 测试基本的AES-GCM加密解密
        String plaintext = "Hello, AES-GCM World!";
        
        String encrypted = springEncryptionUtil.encryptWithGCM(plaintext);
        assertNotNull(encrypted);
        assertNotEquals(plaintext, encrypted);
        
        String decrypted = springEncryptionUtil.decryptWithGCM(encrypted);
        assertEquals(plaintext, decrypted);
    }

    @Test
    void testAESGCMWithSpecialCharacters() {
        // 测试包含特殊字符的数据
        String plaintext = "用户ID:123|时间戳:1699123456|密钥名称:测试密钥|随机数:789";
        
        String encrypted = springEncryptionUtil.encryptWithGCM(plaintext);
        String decrypted = springEncryptionUtil.decryptWithGCM(encrypted);
        
        assertEquals(plaintext, decrypted);
    }

    @Test
    void testGenerateCompatibleApiKeyV2() {
        // 测试生成兼容密钥V2
        Long userId = 123L;
        String keyName = "test-key";
        
        SpringEncryptionUtil.CompatibleKeyResult result = 
            springEncryptionUtil.generateCompatibleApiKeyV2(userId, keyName);
        
        assertNotNull(result);
        assertNotNull(result.getCompatibleKey());
        assertNotNull(result.getSecurityHash());
        assertEquals(123456789L, result.getSnowflakeId());
        
        // 验证密钥格式
        assertTrue(result.getCompatibleKey().startsWith("sk-v2-"));
    }

    @Test
    void testParseCompatibleApiKeyV2() {
        // 测试解析兼容密钥V2
        Long userId = 456L;
        String keyName = "parse-test";
        
        // 生成密钥
        SpringEncryptionUtil.CompatibleKeyResult result = 
            springEncryptionUtil.generateCompatibleApiKeyV2(userId, keyName);
        
        // 解析密钥
        SpringEncryptionUtil.ParseResultV2 parseResult = 
            springEncryptionUtil.parseCompatibleApiKeyV2(result.getCompatibleKey());
        
        assertTrue(parseResult.isValid());
        assertEquals(userId, parseResult.getUserId());
        assertEquals(keyName, parseResult.getKeyName());
        assertNotNull(parseResult.getTimestamp());
        assertEquals("解析成功", parseResult.getMessage());
    }

    @Test
    void testValidateCompatibleKeyV2() {
        // 测试验证兼容密钥V2
        Long userId = 789L;
        String keyName = "validation-test";
        
        SpringEncryptionUtil.CompatibleKeyResult result = 
            springEncryptionUtil.generateCompatibleApiKeyV2(userId, keyName);
        
        boolean isValid = springEncryptionUtil.validateCompatibleKey(result.getCompatibleKey());
        assertTrue(isValid);
    }

    @Test
    void testInvalidKeyFormats() {
        // 测试无效的密钥格式
        assertFalse(springEncryptionUtil.validateCompatibleKey(null));
        assertFalse(springEncryptionUtil.validateCompatibleKey(""));
        assertFalse(springEncryptionUtil.validateCompatibleKey("invalid-key"));
        assertFalse(springEncryptionUtil.validateCompatibleKey("sk-invalid"));
        
        // 测试解析无效密钥
        SpringEncryptionUtil.ParseResultV2 parseResult = 
            springEncryptionUtil.parseCompatibleApiKeyV2("sk-v2-invalid");
        assertFalse(parseResult.isValid());
        assertNotNull(parseResult.getMessage());
    }

    @Test
    void testEncryptionConsistency() {
        // 测试加密的一致性（相同输入应产生不同输出，因为有随机IV）
        String plaintext = "consistency test";
        
        String encrypted1 = springEncryptionUtil.encryptWithGCM(plaintext);
        String encrypted2 = springEncryptionUtil.encryptWithGCM(plaintext);
        
        // 由于使用随机IV，相同明文的加密结果应该不同
        assertNotEquals(encrypted1, encrypted2);
        
        // 但解密结果应该相同
        assertEquals(plaintext, springEncryptionUtil.decryptWithGCM(encrypted1));
        assertEquals(plaintext, springEncryptionUtil.decryptWithGCM(encrypted2));
    }

    @Test
    void testKeyGenerationWithNullOrEmptyKeyName() {
        // 测试空密钥名称的处理
        Long userId = 999L;
        
        SpringEncryptionUtil.CompatibleKeyResult result1 = 
            springEncryptionUtil.generateCompatibleApiKeyV2(userId, null);
        SpringEncryptionUtil.CompatibleKeyResult result2 = 
            springEncryptionUtil.generateCompatibleApiKeyV2(userId, "");
        SpringEncryptionUtil.CompatibleKeyResult result3 = 
            springEncryptionUtil.generateCompatibleApiKeyV2(userId, "   ");
        
        assertNotNull(result1.getCompatibleKey());
        assertNotNull(result2.getCompatibleKey());
        assertNotNull(result3.getCompatibleKey());
        
        // 解析并验证默认密钥名称
        SpringEncryptionUtil.ParseResultV2 parseResult1 = 
            springEncryptionUtil.parseCompatibleApiKeyV2(result1.getCompatibleKey());
        assertEquals("default", parseResult1.getKeyName());
    }

    @Test
    void testInvalidUserIdHandling() {
        // 测试无效用户ID的处理
        assertThrows(IllegalArgumentException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKeyV2(null, "test");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKeyV2(0L, "test");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKeyV2(-1L, "test");
        });
    }

    @Test
    void testEmptyDataEncryption() {
        // 测试空数据加密
        assertThrows(IllegalArgumentException.class, () -> {
            springEncryptionUtil.encryptWithGCM(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            springEncryptionUtil.encryptWithGCM("");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            springEncryptionUtil.encryptWithGCM("   ");
        });
    }

    @Test
    void testInvalidDataDecryption() {
        // 测试无效数据解密
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decryptWithGCM("invalid-base64");
        });
        
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decryptWithGCM("dGVzdA"); // 有效base64但数据太短
        });
    }

    @Test
    void testBackwardCompatibility() {
        // 测试向后兼容性（如果有旧格式密钥的话）
        // 这里可以添加对旧格式密钥的测试
        String oldFormatKey = "sk-test_1699123456_abcd1234";
        
        // 验证旧格式密钥仍然可以被验证（如果实现了的话）
        // 注意：这需要根据实际的旧格式实现来调整
    }
}
